import pandas as pd
import os
import logging
import math
from datetime import datetime
from urllib.parse import urlparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('b6_deduplication.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def extract_domain(url):
    """Extract main domain from URL, removing subdomains and www."""
    if pd.isna(url) or url == '':
        return None

    try:
        # Clean the URL
        url = str(url).strip()

        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Parse the URL
        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Remove www prefix
        if domain.startswith('www.'):
            domain = domain[4:]

        # Remove common subdomains (but keep main domain)
        domain_parts = domain.split('.')
        if len(domain_parts) > 2:
            # Keep only the last two parts (domain.tld)
            domain = '.'.join(domain_parts[-2:])

        return domain if domain else None

    except Exception as e:
        logger.debug(f"Error extracting domain from {url}: {e}")
        return None

def create_info_email(domain):
    """Create info@ email from domain."""
    if pd.isna(domain) or domain == '':
        return None
    return f"info@{domain}"

def load_combined_file_in_chunks(file_path, chunk_size=50000):
    """Load the large combined file in chunks for processing."""
    logger.info(f"Loading combined file: {file_path}")
    
    if not os.path.exists(file_path):
        logger.error(f"File not found: {file_path}")
        return None
    
    try:
        # Get total number of rows first
        total_rows = sum(1 for line in open(file_path, 'r', encoding='utf-8')) - 1  # subtract header
        logger.info(f"Total rows in file: {total_rows:,}")
        
        # Read file in chunks
        chunk_list = []
        chunk_count = 0
        
        for chunk in pd.read_csv(file_path, chunksize=chunk_size, low_memory=False):
            chunk_count += 1
            chunk_list.append(chunk)
            logger.info(f"Loaded chunk {chunk_count}: {len(chunk):,} rows")
        
        # Combine all chunks
        combined_df = pd.concat(chunk_list, ignore_index=True)
        logger.info(f"Successfully loaded {len(combined_df):,} total records")
        
        return combined_df
        
    except Exception as e:
        logger.error(f"Error loading file: {e}")
        return None

def process_domains_and_emails(df):
    """Process domains and create info@ emails if not already present."""
    logger.info("Processing domains and emails...")

    # Check if domain column already exists
    if 'domain' not in df.columns:
        logger.info("Domain column not found, creating from website data...")
        
        # Find website column (could be 'website', 'Website', 'url', etc.)
        website_cols = [col for col in df.columns if 'website' in col.lower() or 'url' in col.lower()]

        if not website_cols:
            logger.warning("No website column found, checking for other URL-like columns")
            # Look for columns that might contain URLs
            for col in df.columns:
                if df[col].dtype == 'object':
                    sample_values = df[col].dropna().head(10).astype(str)
                    if any('.' in str(val) and ('http' in str(val) or 'www' in str(val) or '.com' in str(val)) for val in sample_values):
                        website_cols = [col]
                        logger.info(f"Found potential website column: {col}")
                        break

        if website_cols:
            website_col = website_cols[0]
            logger.info(f"Using website column: {website_col}")

            # Extract domains
            df['domain'] = None
            mask = df[website_col].notna()
            df.loc[mask, 'domain'] = df.loc[mask, website_col].apply(extract_domain)
        else:
            logger.warning("No website column found - creating empty domain column")
            df['domain'] = None
    else:
        logger.info("Domain column already exists")

    # Check if info@ email column exists
    if 'info@ email' not in df.columns:
        logger.info("Creating info@ email column...")
        df['info@ email'] = df['domain'].apply(create_info_email)
    else:
        logger.info("Info@ email column already exists")

    # Log results
    total_records = len(df)
    records_with_domain = df['domain'].notna().sum()
    records_with_email = df['info@ email'].notna().sum()

    logger.info("Domain processing results:")
    logger.info(f"  - Total records: {total_records:,}")
    logger.info(f"  - Records with domain: {records_with_domain:,} ({records_with_domain/total_records*100:.1f}%)")
    logger.info(f"  - Records with info@ email: {records_with_email:,} ({records_with_email/total_records*100:.1f}%)")

    return df

def deduplicate_by_domain(df):
    """Remove duplicate records by domain within the dataset."""
    logger.info("Deduplicating records by domain...")

    initial_count = len(df)

    # Separate records with and without domains
    df_with_domain = df[df['domain'].notna()].copy()
    df_without_domain = df[df['domain'].isna()].copy()

    logger.info(f"  - Initial records: {initial_count:,}")
    logger.info(f"  - Records without domain: {len(df_without_domain):,}")
    logger.info(f"  - Records with domain: {len(df_with_domain):,}")

    # Deduplicate records with domains (keep first occurrence)
    df_deduped = df_with_domain.drop_duplicates(subset=['domain'], keep='first')

    # Combine back with records without domains
    df_final = pd.concat([df_deduped, df_without_domain], ignore_index=True)

    final_count = len(df_final)
    duplicates_removed = len(df_with_domain) - len(df_deduped)

    logger.info(f"  - Duplicates removed: {duplicates_removed:,}")
    logger.info(f"  - Final unique records: {final_count:,}")
    if len(df_with_domain) > 0:
        logger.info(f"  - Deduplication rate: {duplicates_removed/len(df_with_domain)*100:.1f}%")
    else:
        logger.info(f"  - Deduplication rate: 0.0% (no records with domains)")

    return df_final, duplicates_removed

def get_file_size_mb(file_path):
    """Get file size in MB."""
    if os.path.exists(file_path):
        return os.path.getsize(file_path) / (1024 * 1024)
    return 0

def estimate_rows_per_50mb(df, sample_size=1000):
    """Estimate how many rows fit in 50MB based on a sample."""
    logger.info("Estimating optimal file split size...")

    # Take a sample and save it to estimate size
    sample_df = df.head(min(sample_size, len(df)))
    temp_file = "temp_sample_b6_deduped.csv"
    sample_df.to_csv(temp_file, index=False)

    sample_size_mb = get_file_size_mb(temp_file)
    os.remove(temp_file)

    # Calculate rows per MB
    rows_per_mb = sample_size / sample_size_mb

    # Target 45MB to leave some buffer for 50MB limit
    target_mb = 45
    estimated_rows = int(rows_per_mb * target_mb)

    logger.info(f"  - Sample size: {sample_size} rows = {sample_size_mb:.2f} MB")
    logger.info(f"  - Estimated rows per MB: {rows_per_mb:.0f}")
    logger.info(f"  - Target rows per file (45MB): {estimated_rows:,}")

    return estimated_rows

def split_dataframe(df, rows_per_file, base_filename):
    """Split dataframe into multiple files."""
    logger.info(f"Splitting dataframe into files of {rows_per_file:,} rows each...")

    total_rows = len(df)
    num_files = math.ceil(total_rows / rows_per_file)

    logger.info(f"  - Total rows: {total_rows:,}")
    logger.info(f"  - Rows per file: {rows_per_file:,}")
    logger.info(f"  - Number of files: {num_files}")

    created_files = []

    for i in range(num_files):
        start_idx = i * rows_per_file
        end_idx = min((i + 1) * rows_per_file, total_rows)

        # Create chunk
        chunk_df = df.iloc[start_idx:end_idx].copy()

        # Generate filename
        filename = f"{base_filename}_part{i+1:02d}.csv"

        # Save chunk
        chunk_df.to_csv(filename, index=False)

        # Check file size
        file_size_mb = get_file_size_mb(filename)

        logger.info(f"  - Created {filename}: {len(chunk_df):,} rows, {file_size_mb:.1f} MB")

        created_files.append({
            'filename': filename,
            'rows': len(chunk_df),
            'size_mb': file_size_mb
        })

    return created_files

def generate_deduplication_report(initial_count, final_count, duplicates_removed, split_files, output_dir):
    """Generate a detailed deduplication report."""
    report_path = os.path.join(output_dir, 'b6_deduplication_report.txt')

    with open(report_path, 'w') as f:
        f.write("B6 Combined File Deduplication Report\n")
        f.write("=" * 37 + "\n")
        f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("Deduplication Statistics:\n")
        f.write("-" * 26 + "\n")
        f.write(f"Initial records: {initial_count:,}\n")
        f.write(f"Final unique records: {final_count:,}\n")
        f.write(f"Duplicates removed: {duplicates_removed:,}\n")
        f.write(f"Deduplication rate: {duplicates_removed/initial_count*100:.1f}%\n")
        f.write(f"Data retention rate: {final_count/initial_count*100:.1f}%\n\n")

        f.write("Split Files Created:\n")
        f.write("-" * 20 + "\n")
        total_size = 0
        for i, file_info in enumerate(split_files, 1):
            f.write(f"{i:2d}. {file_info['filename']}: {file_info['rows']:,} rows, {file_info['size_mb']:.1f} MB\n")
            total_size += file_info['size_mb']

        f.write(f"\nTotal split files: {len(split_files)}\n")
        f.write(f"Total size: {total_size:.1f} MB\n")
        f.write(f"Average file size: {total_size/len(split_files):.1f} MB\n")
        f.write(f"All files under 50MB: {'Yes' if all(f['size_mb'] < 50 for f in split_files) else 'No'}\n")

    logger.info(f"Deduplication report saved to: {report_path}")

def main():
    """Main function to deduplicate B6 combined file by domain."""
    logger.info("Starting B6 combined file deduplication by domain...")

    # Configuration
    input_file = "b6_combined_all_files.csv"
    base_output_name = "b6_combined_deduped_by_domain"

    try:
        # Step 1: Load the combined file
        logger.info("Step 1: Loading combined file...")
        df = load_combined_file_in_chunks(input_file)
        
        if df is None or df.empty:
            logger.error("Failed to load data. Exiting.")
            return

        initial_count = len(df)

        # Step 2: Process domains and emails
        logger.info("Step 2: Processing domains and emails...")
        df = process_domains_and_emails(df)

        # Step 3: Deduplicate by domain
        logger.info("Step 3: Deduplicating by domain...")
        df_deduped, duplicates_removed = deduplicate_by_domain(df)

        final_count = len(df_deduped)

        # Step 4: Estimate optimal split size and split
        logger.info("Step 4: Estimating optimal split size...")
        rows_per_file = estimate_rows_per_50mb(df_deduped)

        logger.info("Step 5: Splitting data into files...")
        split_files = split_dataframe(df_deduped, rows_per_file, base_output_name)

        # Step 6: Generate deduplication report
        logger.info("Step 6: Generating deduplication report...")
        generate_deduplication_report(initial_count, final_count, duplicates_removed, split_files, ".")

        logger.info("Deduplication completed successfully!")
        logger.info(f"Created {len(split_files)} split files, all under 50MB")

        # Summary
        total_size = sum(f['size_mb'] for f in split_files)
        logger.info(f"\nSUMMARY:")
        logger.info(f"- Original records: {initial_count:,}")
        logger.info(f"- Final unique records: {final_count:,}")
        logger.info(f"- Duplicates removed: {duplicates_removed:,}")
        logger.info(f"- Deduplication rate: {duplicates_removed/initial_count*100:.1f}%")
        logger.info(f"- Split files created: {len(split_files)}")
        logger.info(f"- Total split files size: {total_size:.1f} MB")

    except Exception as e:
        logger.error(f"Deduplication failed: {e}")
        raise

if __name__ == "__main__":
    main()
